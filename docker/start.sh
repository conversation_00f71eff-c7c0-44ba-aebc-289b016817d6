#!/bin/bash

# 社交媒体数据统计系统 Docker 启动脚本
# Social Media Data Statistics System Docker Start Script
# 
# 使用方法:
#   ./start.sh           - 标准启动
#   ./start.sh --install - 预先安装 Composer 依赖（可选）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "Docker 环境检查通过"
}

# 检查必要的目录和文件
check_requirements() {
    log_info "检查项目文件..."
    
    # 检查必要的目录
    required_dirs=("www" "system" "docker")
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            log_error "缺少必要目录: $dir"
            exit 1
        fi
    done
    
    # 检查 composer.json 文件
    if [ ! -f "composer.json" ]; then
        log_error "缺少 composer.json 文件"
        exit 1
    fi
    
    # 检查配置文件
    if [ ! -f "system/config/my.php" ]; then
        log_error "缺少数据库配置文件: system/config/my.php"
        exit 1
    fi
    
    # vendor 目录会在 Docker 构建过程中生成，这里不需要检查
    if [ ! -d "vendor" ]; then
        log_info "vendor 目录不存在，将在 Docker 构建过程中通过 Composer 生成"
    else
        log_success "vendor 目录已存在"
    fi
    
    # 创建必要的目录
    mkdir -p logs
    mkdir -p www/data
    mkdir -p system/tmp
    
    # 设置权限
    chmod -R 755 www/data
    chmod -R 755 system/tmp
    chmod -R 755 logs
    
    log_success "项目文件检查完成"
}

# 可选：预先安装 Composer 依赖
install_composer_dependencies() {
    if command -v composer &> /dev/null; then
        log_info "检测到本地 Composer，预先安装依赖..."
        composer install --no-dev --optimize-autoloader
        log_success "Composer 依赖安装完成"
    else
        log_warning "未检测到本地 Composer，将在 Docker 构建过程中安装依赖"
    fi
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    # 从配置文件读取数据库信息
    DB_HOST=$(grep "db->host" system/config/my.php | cut -d"'" -f2)
    DB_PORT=$(grep "db->port" system/config/my.php | cut -d"'" -f2)
    DB_NAME=$(grep "db->name" system/config/my.php | cut -d"'" -f2)
    DB_USER=$(grep "db->user" system/config/my.php | cut -d"'" -f2)
    DB_PASSWORD=$(grep "db->password" system/config/my.php | cut -d"'" -f2)
    
    log_info "数据库配置: $DB_USER@$DB_HOST:$DB_PORT/$DB_NAME"
    
    # 这里可以添加数据库连接测试
    # 由于是外部数据库，暂时跳过连接测试
    log_warning "请确保外部数据库 $DB_HOST:$DB_PORT 可以正常访问"
}

# 构建和启动容器
start_containers() {
    log_info "构建 Docker 镜像..."
    docker compose build --no-cache
    
    log_info "启动容器..."
    docker compose up -d
    
    # 等待容器启动
    log_info "等待容器启动..."
    sleep 10
    
    # 检查容器状态
    if docker compose ps | grep -q "Up"; then
        log_success "容器启动成功"
    else
        log_error "容器启动失败"
        docker compose logs
        exit 1
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库表..."
    
    # 执行数据库初始化脚本
    if [ -f "system/db/smdata_tables.sql" ]; then
        log_info "发现数据库表结构文件，请手动执行以下SQL文件："
        log_info "system/db/smdata_tables.sql"
        log_warning "请确保数据库表已正确创建"
    fi
}

# 检查PHP扩展
check_php_extensions() {
    log_info "检查 PHP 扩展..."
    
    # EasyWeChat 6 所需的扩展
    required_extensions=("curl" "openssl" "simplexml" "fileinfo" "mbstring" "xml")
    
    for ext in "${required_extensions[@]}"; do
        if docker compose exec -T web php -m | grep -q "$ext"; then
            log_success "PHP 扩展 $ext 已安装"
        else
            log_error "PHP 扩展 $ext 未安装"
        fi
    done
    
    log_info "PHP 扩展检查完成"
}

# 显示访问信息
show_access_info() {
    log_success "部署完成！"
    echo ""
    echo "访问信息："
    echo "- 前台地址: http://localhost:8080"
    echo "- 后台地址: http://localhost:8080/admin.php"
    echo "- 健康检查: http://localhost:8080/health"
    echo ""
    echo "容器管理命令："
    echo "- 查看日志: docker compose logs -f"
    echo "- 停止服务: docker compose down"
    echo "- 重启服务: docker compose restart"
    echo "- 查看状态: docker compose ps"
    echo ""
    echo "数据同步："
    echo "- 手动同步: docker compose exec web php system/script/daily_sync.php"
    echo "- 查看同步日志: docker compose exec web tail -f /var/log/app/wechat_sync.log"
}

# 主函数
main() {
    echo "========================================"
    echo "社交媒体数据统计系统 Docker 部署脚本"
    echo "========================================"
    echo ""
    
    # 检查是否需要预安装 Composer 依赖
    if [[ "$1" == "--install" ]]; then
        log_info "启用预安装模式"
        check_docker
        check_requirements
        install_composer_dependencies
        check_database
        start_containers
        init_database
        check_php_extensions
        show_access_info
    else
        check_docker
        check_requirements
        check_database
        start_containers
        init_database
        check_php_extensions
        show_access_info
    fi
}

# 执行主函数
main "$@"
