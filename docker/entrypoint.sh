#!/bin/bash

# 容器启动脚本
# Container startup script

set -e

# 创建必要的目录
echo "Creating necessary directories..."
mkdir -p /var/www/html/system/tmp/sessions
mkdir -p /var/www/html/system/tmp/cache
mkdir -p /var/www/html/system/tmp/wsdlcache
mkdir -p /var/www/html/www/data
mkdir -p /var/log/app

# 设置正确的权限
echo "Setting correct permissions..."
chown -R www-data:www-data /var/www/html/system/tmp
chown -R www-data:www-data /var/www/html/www/data
chown -R www-data:www-data /var/log/app

chmod -R 777 /var/www/html/system/tmp
chmod -R 777 /var/www/html/www/data
chmod -R 755 /var/log/app

# 特别确保session目录权限
chmod 777 /var/www/html/system/tmp/sessions
chown www-data:www-data /var/www/html/system/tmp/sessions

# 检查session目录权限
echo "Checking session directory permissions..."
ls -la /var/www/html/system/tmp/

# 检查PHP session配置
echo "Checking PHP session configuration..."
php -i | grep -E "session.save_path|session.save_handler" || true

# 测试session目录写权限
echo "Testing session directory write permissions..."
if [ -w /var/www/html/system/tmp/sessions ]; then
    echo "Session directory is writable"
    # 创建测试文件
    touch /var/www/html/system/tmp/sessions/test_session_file
    if [ -f /var/www/html/system/tmp/sessions/test_session_file ]; then
        echo "Session directory write test successful"
        rm -f /var/www/html/system/tmp/sessions/test_session_file
    else
        echo "Session directory write test failed"
    fi
else
    echo "Session directory is not writable"
    exit 1
fi

# 启动 Supervisor
echo "Starting Supervisor..."
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
