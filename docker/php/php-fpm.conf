[www]
user = www-data
group = www-data

listen = 127.0.0.1:9000
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500

; 慢日志
slowlog = /var/log/php-fpm-slow.log
request_slowlog_timeout = 10s

; 进程超时
request_terminate_timeout = 300s

; 环境变量
env[HOSTNAME] = $HOSTNAME
env[PATH] = /usr/local/bin:/usr/bin:/bin
env[TMP] = /tmp
env[TMPDIR] = /tmp
env[TEMP] = /tmp

; PHP 设置
php_admin_value[sendmail_path] = /usr/sbin/sendmail -t -i -f <EMAIL>
php_flag[display_errors] = off
php_admin_value[error_log] = /var/log/php-fpm-error.log
php_admin_flag[log_errors] = on
php_admin_value[memory_limit] = 512M
php_admin_value[max_execution_time] = 300
php_admin_value[upload_max_filesize] = 100M
php_admin_value[post_max_size] = 100M

; 安全设置
php_admin_value[open_basedir] = /var/www/html:/tmp:/usr/share/php
php_admin_value[disable_functions] = exec,passthru,shell_exec,system,proc_open,popen

; 会话设置
php_value[session.save_handler] = files
php_value[session.save_path] = /var/lib/php/sessions

; 访问日志
access.log = /var/log/php-fpm-access.log
access.format = "%R - %u %t \"%m %r%Q%q\" %s %f %{mili}d %{kilo}M %C%%"

; 状态页面
pm.status_path = /status
ping.path = /ping
ping.response = pong

; 清理环境
clear_env = no
