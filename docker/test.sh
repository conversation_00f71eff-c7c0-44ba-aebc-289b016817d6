#!/bin/bash

# 部署验证测试脚本
# 用于验证 Docker 环境部署是否成功

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试容器状态
test_containers() {
    log_info "检查容器状态..."
    
    # 检查容器是否运行
    if ! docker compose ps | grep -q "Up"; then
        log_error "容器未正常运行"
        docker compose ps
        return 1
    fi
    
    log_success "所有容器正常运行"
}

# 测试 Web 服务
test_web_service() {
    log_info "测试 Web 服务..."
    
    # 等待服务启动
    sleep 5
    
    # 测试健康检查端点
    if curl -f -s http://localhost:8080/health > /dev/null; then
        log_success "健康检查端点正常"
    else
        log_error "健康检查端点失败"
        return 1
    fi
    
    # 测试前台页面
    if curl -f -s http://localhost:8080/ > /dev/null; then
        log_success "前台页面可访问"
    else
        log_warning "前台页面可能有问题"
    fi
    
    # 测试后台页面
    if curl -f -s http://localhost:8080/admin.php > /dev/null; then
        log_success "后台页面可访问"
    else
        log_warning "后台页面可能有问题"
    fi
}

# 测试数据库连接
test_database() {
    log_info "测试数据库连接..."
    
    # 在容器内测试数据库连接
    if docker compose exec -T web php -r "
        include '/var/www/html/system/config/my.php';
        try {
            \$pdo = new PDO(
                'mysql:host=' . \$config->db->host . ';port=' . \$config->db->port . ';dbname=' . \$config->db->name,
                \$config->db->user,
                \$config->db->password
            );
            echo 'Database connection successful';
        } catch(PDOException \$e) {
            echo 'Database connection failed: ' . \$e->getMessage();
            exit(1);
        }
    "; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败"
        return 1
    fi
}

# 测试 Redis 连接
test_redis() {
    log_info "测试 Redis 连接..."
    
    if docker compose exec -T redis redis-cli ping | grep -q "PONG"; then
        log_success "Redis 连接正常"
    else
        log_error "Redis 连接失败"
        return 1
    fi
}

# 测试文件权限
test_permissions() {
    log_info "测试文件权限..."
    
    # 测试日志目录写权限
    if docker compose exec -T web touch /var/log/app/test.log; then
        docker compose exec -T web rm -f /var/log/app/test.log
        log_success "日志目录写权限正常"
    else
        log_error "日志目录写权限异常"
        return 1
    fi
    
    # 测试数据目录写权限
    if docker compose exec -T web touch /var/www/html/www/data/test.txt; then
        docker compose exec -T web rm -f /var/www/html/www/data/test.txt
        log_success "数据目录写权限正常"
    else
        log_error "数据目录写权限异常"
        return 1
    fi
    
    # 测试临时目录写权限
    if docker compose exec -T web touch /var/www/html/system/tmp/test.tmp; then
        docker compose exec -T web rm -f /var/www/html/system/tmp/test.tmp
        log_success "临时目录写权限正常"
    else
        log_error "临时目录写权限异常"
        return 1
    fi
}

# 测试定时任务
test_cron() {
    log_info "测试定时任务..."
    
    # 检查 cron 容器是否运行
    if docker compose ps cron | grep -q "Up"; then
        log_success "定时任务容器正常运行"
    else
        log_error "定时任务容器未运行"
        return 1
    fi
    
    # 检查 crontab 是否正确加载
    if docker compose exec -T cron crontab -l | grep -q "daily_sync.php"; then
        log_success "定时任务配置正常"
    else
        log_warning "定时任务配置可能有问题"
    fi
}

# 测试同步脚本
test_sync_scripts() {
    log_info "测试同步脚本..."
    
    # 测试脚本是否可执行
    if docker compose exec -T web test -x /var/www/html/system/script/daily_sync.php; then
        log_success "同步脚本可执行"
    else
        log_error "同步脚本不可执行"
        return 1
    fi
    
    # 测试脚本语法
    if docker compose exec -T web php -l /var/www/html/system/script/daily_sync.php > /dev/null; then
        log_success "同步脚本语法正确"
    else
        log_error "同步脚本语法错误"
        return 1
    fi
}

# 性能测试
test_performance() {
    log_info "进行简单性能测试..."
    
    # 使用 ab 进行简单的并发测试
    if command -v ab &> /dev/null; then
        log_info "执行并发测试 (10 个并发，100 个请求)..."
        ab -n 100 -c 10 http://localhost:8080/health > /tmp/ab_test.log 2>&1
        
        if grep -q "Complete requests:.*100" /tmp/ab_test.log; then
            log_success "性能测试通过"
        else
            log_warning "性能测试可能有问题，请查看 /tmp/ab_test.log"
        fi
    else
        log_warning "未安装 ab 工具，跳过性能测试"
    fi
}

# 显示系统信息
show_system_info() {
    log_info "系统信息："
    echo "Docker 版本: $(docker --version)"
    echo "Docker Compose 版本: $(docker compose --version)"
    echo ""
    
    log_info "容器资源使用情况："
    docker compose exec -T web ps aux --sort=-%cpu | head -10
    echo ""
    
    log_info "磁盘使用情况："
    docker compose exec -T web df -h
    echo ""
    
    log_info "内存使用情况："
    docker compose exec -T web free -h
}

# 主函数
main() {
    echo "========================================"
    echo "社交媒体数据统计系统 部署验证测试"
    echo "========================================"
    echo ""
    
    local failed_tests=0
    
    # 执行各项测试
    test_containers || ((failed_tests++))
    test_web_service || ((failed_tests++))
    test_database || ((failed_tests++))
    test_redis || ((failed_tests++))
    test_permissions || ((failed_tests++))
    test_cron || ((failed_tests++))
    test_sync_scripts || ((failed_tests++))
    test_performance
    
    echo ""
    echo "========================================"
    
    if [ $failed_tests -eq 0 ]; then
        log_success "所有测试通过！系统部署成功！"
        echo ""
        show_system_info
        echo ""
        echo "系统已准备就绪，可以开始使用："
        echo "- 前台地址: http://localhost:8080"
        echo "- 后台地址: http://localhost:8080/admin.php"
        echo "- 健康检查: http://localhost:8080/health"
        exit 0
    else
        log_error "有 $failed_tests 项测试失败，请检查系统配置"
        echo ""
        log_info "查看详细日志："
        echo "docker compose logs -f"
        exit 1
    fi
}

# 执行主函数
main "$@"
