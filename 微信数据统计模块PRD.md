# 微信数据统计模块（smdata）产品需求文档

## 1. 项目概述

### 1.1 项目背景
为了更好地分析微信公众号/服务号的运营数据，需要开发一个数据统计模块，通过调用微信官方数据分析接口，将数据同步到本地数据库，并提供可视化的数据展示。

### 1.2 项目目标
- 自动同步微信公众号的用户分析、图文分析、消息分析数据
- 提供数据可视化展示界面
- 支持定时任务自动更新数据
- 与现有微信模块集成，复用公众号配置信息

## 2. 功能需求

### 2.1 核心功能
1. **数据同步功能**
   - 用户分析数据同步（新增用户、取消关注用户、净增用户、累计用户）
   - 图文分析数据同步（图文页阅读人数、次数、分享转发等）
   - 消息分析数据同步（消息发送人数、次数、拦截等）

2. **数据展示功能**
   - 用户增长趋势图表
   - 图文阅读统计图表
   - 消息互动统计图表
   - 数据报表导出

3. **管理功能**
   - 数据同步日志查看
   - 手动触发数据同步
   - 同步时间范围设置

### 2.2 技术需求
- 使用 EasyWeChat 库调用微信数据分析接口
- 数据存储使用 MySQL 数据库
- 支持定时任务执行
- 响应式前端界面

## 3. 数据库设计

### 3.1 表结构设计

#### 3.1.1 用户分析数据表 (smd_wx_user_summary)
```sql
CREATE TABLE `smd_wx_user_summary` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `public_id` smallint(5) unsigned NOT NULL COMMENT '公众号ID',
  `ref_date` date NOT NULL COMMENT '数据的日期',
  `user_source` tinyint(4) NOT NULL COMMENT '用户的渠道，数值代表的含义如下：0代表其他合计 1代表公众号搜索 17代表名片分享 35代表扫描二维码 39代表图文页右上角菜单 43代表图文页内公众号名称 51代表支付后关注 57代表图文页内名称 75代表公众号文章广告 78代表朋友圈广告',
  `new_user` int(11) NOT NULL DEFAULT '0' COMMENT '新增的用户数量',
  `cancel_user` int(11) NOT NULL DEFAULT '0' COMMENT '取消关注的用户数量，new_user减去cancel_user即为净增用户数量',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_data` (`public_id`, `ref_date`, `user_source`),
  KEY `idx_public_date` (`public_id`, `ref_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信用户分析数据表';
```

#### 3.1.2 图文分析数据表 (smd_wx_article_summary)
```sql
CREATE TABLE `smd_wx_article_summary` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `public_id` smallint(5) unsigned NOT NULL COMMENT '公众号ID',
  `ref_date` date NOT NULL COMMENT '数据的日期',
  `msgid` varchar(64) NOT NULL COMMENT '消息id',
  `title` varchar(255) NOT NULL COMMENT '图文消息的标题',
  `int_page_read_user` int(11) NOT NULL DEFAULT '0' COMMENT '图文页（点击群发图文卡片进入的页面）的阅读人数',
  `int_page_read_count` int(11) NOT NULL DEFAULT '0' COMMENT '图文页的阅读次数',
  `ori_page_read_user` int(11) NOT NULL DEFAULT '0' COMMENT '原文页（点击阅读原文进入的页面）的阅读人数',
  `ori_page_read_count` int(11) NOT NULL DEFAULT '0' COMMENT '原文页的阅读次数',
  `share_user` int(11) NOT NULL DEFAULT '0' COMMENT '分享的人数',
  `share_count` int(11) NOT NULL DEFAULT '0' COMMENT '分享的次数',
  `add_to_fav_user` int(11) NOT NULL DEFAULT '0' COMMENT '收藏的人数',
  `add_to_fav_count` int(11) NOT NULL DEFAULT '0' COMMENT '收藏的次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_data` (`public_id`, `ref_date`, `msgid`),
  KEY `idx_public_date` (`public_id`, `ref_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信图文分析数据表';
```

#### 3.1.3 消息分析数据表 (smd_wx_upstream_msg)
```sql
CREATE TABLE `smd_wx_upstream_msg` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `public_id` smallint(5) unsigned NOT NULL COMMENT '公众号ID',
  `ref_date` date NOT NULL COMMENT '数据的日期',
  `msg_type` tinyint(4) NOT NULL COMMENT '消息类型，代表含义如下：1代表文字 2代表图片 3代表语音 4代表视频 6代表第三方应用消息（链接消息）',
  `msg_user` int(11) NOT NULL DEFAULT '0' COMMENT '上行发送了（向公众号发送了）消息的用户数',
  `msg_count` int(11) NOT NULL DEFAULT '0' COMMENT '上行发送了消息的消息总数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_data` (`public_id`, `ref_date`, `msg_type`),
  KEY `idx_public_date` (`public_id`, `ref_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信消息分析数据表';
```

#### 3.1.4 数据同步日志表 (smd_wx_sync_log)
```sql
CREATE TABLE `smd_wx_sync_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `public_id` smallint(5) unsigned NOT NULL COMMENT '公众号ID',
  `sync_type` varchar(20) NOT NULL COMMENT '同步类型：user_summary, article_summary, upstream_msg',
  `sync_date` date NOT NULL COMMENT '同步的数据日期',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '同步状态：0失败 1成功',
  `error_msg` text COMMENT '错误信息',
  `sync_count` int(11) NOT NULL DEFAULT '0' COMMENT '同步的记录数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_public_type_date` (`public_id`, `sync_type`, `sync_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据同步日志表';
```

## 4. 接口设计

### 4.1 微信API接口调用
- **用户分析数据接口**: `datacube/getusersummary`
- **图文分析数据接口**: `datacube/getarticlesummary`
- **消息分析数据接口**: `datacube/getupstreammsg`

### 4.2 模块内部方法
- `syncUserSummary($publicId, $startDate, $endDate)` - 同步用户分析数据
- `syncArticleSummary($publicId, $startDate, $endDate)` - 同步图文分析数据
- `syncUpstreamMsg($publicId, $startDate, $endDate)` - 同步消息分析数据
- `getUserSummaryData($publicId, $startDate, $endDate)` - 获取用户分析数据
- `getArticleSummaryData($publicId, $startDate, $endDate)` - 获取图文分析数据
- `getUpstreamMsgData($publicId, $startDate, $endDate)` - 获取消息分析数据

## 5. 页面设计

### 5.1 页面结构
- **数据概览页** (`index`) - 显示各类数据的汇总信息
- **用户分析页** (`user`) - 用户增长趋势、来源分析
- **图文分析页** (`article`) - 图文阅读、分享、收藏统计
- **消息分析页** (`message`) - 消息互动统计
- **同步日志页** (`synclog`) - 数据同步日志查看
- **设置页** (`setting`) - 同步配置设置

### 5.2 功能特性
- 支持日期范围筛选
- 图表可视化展示
- 数据导出功能
- 响应式设计

## 6. 定时任务设计

### 6.1 脚本文件
- `system/script/sync_wechat_data.php` - 主同步脚本

### 6.2 执行逻辑
1. 获取所有已配置的微信公众号
2. 对每个公众号执行数据同步
3. 记录同步日志
4. 异常处理和重试机制

### 6.3 建议执行频率
- 每日凌晨2点执行一次
- 同步前一天的数据

## 7. 开发计划

### 7.1 开发阶段
1. **阶段一**: 创建模块基础结构和数据库表
2. **阶段二**: 实现数据同步功能
3. **阶段三**: 开发前端展示页面
4. **阶段四**: 创建定时任务脚本
5. **阶段五**: 测试和优化

### 7.2 技术要点
- 需要在 `system/config/table.php` 中添加新表的常量定义
- 使用现有的 EasyWeChat 库进行API调用
- 遵循现有项目的代码规范和架构模式

## 8. 风险评估

### 8.1 技术风险
- 微信API调用频率限制
- 数据量大时的性能问题
- 网络异常导致的同步失败

### 8.2 解决方案
- 实现API调用频率控制
- 分批处理大量数据
- 完善的错误处理和重试机制
