# 使用官方 PHP 8.3 FPM 镜像
FROM php:8.3-fpm

# 设置工作目录
WORKDIR /var/www/html

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libcurl4-openssl-dev \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libzip-dev \
    libssl-dev \
    zip \
    unzip \
    nginx \
    supervisor \
    cron \
    && rm -rf /var/lib/apt/lists/*

# 安装 PHP 扩展
RUN docker-php-ext-install \
    pdo_mysql \
    exif \
    pcntl \
    bcmath \
    gd \
    zip \
    simplexml \
    fileinfo

# 安装 Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# 先复制 composer 文件以利用 Docker 缓存
COPY composer.json composer.lock* ./

# 安装 Composer 依赖
RUN composer install --no-dev --optimize-autoloader --no-scripts

# 复制其余项目文件
COPY . /var/www/html

# 运行 composer 脚本（如果有的话）
RUN composer run-script post-install-cmd --no-dev || true

# 创建session目录并设置权限
RUN mkdir -p /var/www/html/system/tmp/sessions \
    && chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html \
    && chmod -R 777 /var/www/html/www/data \
    && chmod -R 777 /var/www/html/system/tmp

# 复制 Nginx 配置
COPY docker/nginx/default.conf /etc/nginx/sites-available/default

# 复制 PHP-FPM 配置
COPY docker/php/php-fpm.conf /usr/local/etc/php-fpm.d/www.conf
COPY docker/php/php.ini /usr/local/etc/php/php.ini

# 复制 Supervisor 配置
COPY docker/supervisor/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# 复制并安装 crontab
COPY docker/cron/crontab /etc/cron.d/app-cron
RUN chmod 0644 /etc/cron.d/app-cron \
    && crontab /etc/cron.d/app-cron \
    && touch /var/log/cron.log

# 创建日志目录
RUN mkdir -p /var/log/app

# 复制启动脚本
COPY docker/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# 暴露端口
EXPOSE 80

# 使用启动脚本
CMD ["/entrypoint.sh"]
