services:
  web:
    build: .
    container_name: social_media_data_web
    ports:
      - "8080:80"
    volumes:
      # 挂载整个项目目录，这样代码修改会直接生效
      - .:/var/www/html
      - ./logs:/var/log/app
    environment:
      - PHP_ENV=production
      - TZ=Asia/Shanghai
    restart: unless-stopped
    networks:
      - social_media_network
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    container_name: social_media_data_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass "redis_password_123"
    restart: unless-stopped
    networks:
      - social_media_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 定时任务容器
  cron:
    build: .
    container_name: social_media_data_cron
    volumes:
      # 挂载整个项目目录，这样代码修改会直接生效
      - .:/var/www/html
      - ./logs:/var/log/app
      - ./docker/cron/crontab:/etc/cron.d/social-media-cron
    environment:
      - PHP_ENV=production
      - TZ=Asia/Shanghai
    command: >
      sh -c "
        chmod 0644 /etc/cron.d/social-media-cron &&
        crontab /etc/cron.d/social-media-cron &&
        cron -f
      "
    restart: unless-stopped
    networks:
      - social_media_network
    depends_on:
      - web

volumes:
  redis_data:
    driver: local

networks:
  social_media_network:
    driver: bridge
