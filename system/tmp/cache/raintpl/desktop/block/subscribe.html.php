<?php if(!class_exists('raintpl')){exit;}?><?php if(!defined("RUN_MODE")){ ?>

<?php echo die(); ?>

<?php } ?>



<?php $block->content = json_decode($block->content);$this->var['block'] = $block;?>

<?php echo $app->loadLang('blog');?>

<?php if(!empty($block->content->fixInNav)){ ?>

<div class='block-subscribe hidden'>
  <ul class='nav navbar-nav navbar-right'>
    <li class='nav-system-blog'>
      <?php echo html::a(helper::createLink('rss', 'index', 'type=blog', '', 'xml'), "<i class='icon-rss text-warning'></i> " . $lang->blog->subscribe, "target='_blank'"); ?>

    </li>
  </ul>
</div>
<script>
$('#blogNav ul.navbar-nav').last().after($('.block-subscribe').html());
</script>
<?php }else{ ?>

<div id="block<?php echo $block->id; ?>" class='panel-pure panel'>
  <?php echo html::a(helper::createLink('rss', 'index', 'type=blog', '', 'xml'), "<i class='icon-rss text-warning'></i> " . $lang->blog->subscribe, "target='_blank' class='btn btn-lg btn-block'"); ?>

</div>
<?php } ?>

