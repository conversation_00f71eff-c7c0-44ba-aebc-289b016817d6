<?php if(!class_exists('raintpl')){exit;}?><?php if(!defined("RUN_MODE")){ ?>



<?php echo die(); ?>

<?php } ?>


<?php $tpl = new RainTPL;$tpl->assign($this->var);$tpl->draw($control->loadModel('ui')->getEffectViewFile('default', 'blog', 'header'));?>



<?php if(isset($category)){ ?>



<?php $path=$this->var['path'] = array_keys($category->pathNames);?>

<?php } ?> 
<?php if(!empty($path)){ ?>



<?php echo js::set('path', $path); ?>

<?php } ?>

<?php if(!empty($category->id)){ ?>



<?php echo js::set('categoryID', $category->id); ?>

<?php } ?>

<?php if(!empty($category->id)){ ?>



<?php echo js::set('pageLayout', $control->block->getLayoutScope('blog_index', $category->id)); ?>

<?php } ?>

<?php $root=$this->var['root'] = '<li>' . $control->lang->currentPos . $control->lang->colon .  html::a($control->inlink('index'), $lang->blog->home) . '</li>';?>

<?php if(!empty($category)){ ?>



<?php echo $common->printPositionBar($category, '', '', $root); ?>

<?php } ?>

<?php if(isset($articleIdList)){ ?>



  <script><?php echo "place" . md5(time()). "='" . $config->idListPlaceHolder . $articleIdList . $config->idListPlaceHolder . "';"; ?></script>
<?php }else{ ?>

  <script><?php echo "place" . md5(time()) . "='" . $config->idListPlaceHolder . '' . $config->idListPlaceHolder . "';"; ?></script>
<?php } ?>

<div class='row blocks' data-region='blog_index-topBanner'><?php echo $control->block->printRegion($layouts, 'blog_index', 'topBanner', true);?>


</div>
<div class='row' id='columns' data-page='blog_index'>
  <?php if(!empty($layouts['blog_index']['side']) and !empty($sideFloat) && $sideFloat != 'hidden'){ ?>



    <div class="col-md-<?php echo 12 - $sideGrid; ?> col-main{if($sideFloat === 'left') echo ' pull-right'}">
  <?php }else{ ?>

    <div class="col-md-12">
  <?php } ?>

    <div class='row blocks' data-region='blog_index-top'><?php echo $control->block->printRegion($layouts, 'blog_index', 'top', true);?>


</div>
    <div id='blogList'>
      <?php foreach($sticks as $stick): ?>



        <?php if(!isset($category)){ ?>



<?php $category=$this->var['category'] = array_shift($stick->categories);?>

<?php } ?>

        <?php $url=$this->var['url'] = inlink('view', "id=$stick->id", "category={$category->alias}&name=$stick->alias");?>



        <div class="card" data-ve='blog' id='blog<?php echo $stick->id; ?>'>
          <?php if(!empty($stick->image)){ ?>



          <?php $pull=$this->var['pull']     = (isset($control->config->blog->imagePosition) and $control->config->blog->imagePosition == 'left') ? 'pull-left' : 'pull-right';?>

          <?php $imageURL=$this->var['imageURL'] = !empty($control->config->blog->imageSize) ? $control->config->blog->imageSize . 'URL' : 'smallURL';?>

          <div class='media <?php echo $pull; ?>' style="max-width: <?php echo !empty($control->config->blog->imageWidth) ? $control->config->blog->imageWidth . 'px' : '180px'; ?>">
            <?php $title=$this->var['title'] = $stick->image->primary->title ? $stick->image->primary->title : $stick->title;?>

            <?php $stick->image->primary->objectType = 'blog';$this->var['stick'] = $stick;?>

            <?php echo html::a($url, html::image($control->loadModel('file')->printFileURL($stick->image->primary, $imageURL), "title='$title' class='thumbnail'")); ?>



          </div>
          <?php } ?>

          <h4 class='card-heading'>
            <?php echo html::a($url, $stick->title, "style='color:{$stick->titleColor}'"); ?>



            <span class='label label-danger'><?php echo $lang->article->stick; ?></span>
          </h4>
          <div class='card-content text-muted'>
            <?php echo $stick->summary;?>

          </div>
          <div class="card-actions text-muted">
            &nbsp; <span data-toggle='tooltip' title='<?php printf($lang->article->lblAddedDate, formatTime($stick->addedDate)); ?>


'><i class="icon-time"></i> <?php echo date('Y/m/d', strtotime($stick->addedDate)); ?>


</span>
            &nbsp; <span data-toggle='tooltip' title='<?php printf($lang->article->lblAuthor, $stick->author); ?>


'><i class="icon-user"></i> <?php echo $stick->author; ?></span>
            &nbsp; <span data-toggle='tooltip' title='<?php printf($lang->article->lblViews, $config->viewsPlaceholder . $stick->id . $config->viewsPlaceholder); ?>


'><i class="icon-eye-open"></i> <?php echo $config->viewsPlaceholder . $stick->id . $config->viewsPlaceholder; ?></span>
            <?php if(commonModel::isAvailable('message') and isset($stick->comments) and $stick->comments){ ?>


&nbsp; <a href="<?php echo $url . '#commentForm'; ?>"><span data-toggle='tooltip' title='<?php printf($lang->article->lblComments, $stick->comments); ?>


'><i class="icon-comments-alt"></i> <?php echo $stick->comments; ?></span></a><?php } ?>

              <?php if(!empty($config->blog->showCategory)){ ?>



                <?php if($config->blog->categoryLevel == 'first'){ ?>



                  <span>[ <?php echo ($config->blog->categoryName == 'full' or empty(zget($topCategoryList, $stick->category->id)->abbr)) ? zget($topCategoryList, $stick->category->id)->name : zget($topCategoryList, $stick->category->id)->abbr; ?> ]</span>
                <?php }else{ ?>

                  <span>[ <?php echo ($config->blog->categoryName == 'full' or empty($stick->category->abbr)) ? $stick->category->name : $stick->category->abbr; ?> ]</span>
                <?php } ?>

<?php } ?>            
          </div>
        </div>
        <?php unset($articles[$stick->id]); ?>



      <?php endforeach; ?>

      <?php foreach($articles as $article): ?>



        <?php if(!isset($category)){ ?>


<?php $category=$this->var['category'] = array_shift($article->categories);?>

<?php } ?>

        <?php $url=$this->var['url'] = inlink('view', "id=$article->id", "category={$category->alias}&name=$article->alias");?>



        <div class="card" data-ve='blog' id='blog<?php echo $article->id;?>'>
          <?php if(!empty($article->image)){ ?>



            <?php $pull=$this->var['pull']     = (isset($control->config->blog->imagePosition) and $control->config->blog->imagePosition == 'left') ? 'pull-left' : 'pull-right';?>

            <?php $imageURL=$this->var['imageURL'] = !empty($control->config->blog->imageSize) ? $control->config->blog->imageSize . 'URL' : 'smallURL';?>

            <div class='media <?php echo $pull; ?>' style="max-width: <?php echo !empty($control->config->blog->imageWidth) ? $control->config->blog->imageWidth . 'px' : '180px'; ?>">
              <?php $title=$this->var['title'] = $article->image->primary->title ? $article->image->primary->title : $article->title;?>

              <?php $article->image->primary->objectType = 'blog';$this->var['article'] = $article;?>

              <?php echo html::a($url, html::image($control->loadModel('file')->printFileURL($article->image->primary, $imageURL), "title='{$title}' class='thumbnail'")); ?>



            </div>
          <?php } ?>

          <h4 class='card-heading'><?php echo html::a($url, $article->title, "style='color:{$article->titleColor}'"); ?>


</h4>
          <div class='card-content text-muted'>
            <?php echo $article->summary; ?>

          </div>
          <div class="card-actions text-muted">
            <span data-toggle='tooltip' title='<?php printf($lang->article->lblAddedDate, formatTime($article->addedDate)); ?>


'><i class="icon-time"></i> <?php echo date('Y/m/d', strtotime($article->addedDate)); ?>


</span>
            &nbsp; <span data-toggle='tooltip' title='<?php printf($lang->article->lblAuthor, $article->author); ?>


'><i class="icon-user"></i> <?php echo $article->author; ?></span>
            &nbsp; <span data-toggle='tooltip' title='<?php printf($lang->article->lblViews, $config->viewsPlaceholder . $article->id . $config->viewsPlaceholder); ?>


'><i class="icon-eye-open"></i> <?php echo $config->viewsPlaceholder . $article->id . $config->viewsPlaceholder; ?></span>
            <?php if(commonModel::isAvailable('message') and $article->comments){ ?>


&nbsp; <a href="<?php echo $url . '#commentForm'; ?>span data-toggle='tooltip' title='<?php printf($lang->article->lblComments, $article->comments); ?>


'><i class="icon-comments-alt"></i> <?php echo $article->comments; ?></span></a><?php } ?>

              <?php if(isset($config->blog->showCategory) and $config->blog->showCategory){ ?>



                <?php if($config->blog->categoryLevel == 'first'){ ?>



                    <?php echo "<span>["; ?>

                    <?php echo ($config->blog->categoryName == 'full' or empty(zget($topCategoryList, $article->category->id)->abbr)) ? zget($topCategoryList, $article->category->id)->name : zget($topCategoryList, $article->category->id)->abbr; ?>

                    <?php echo "]</span>"; ?>

                <?php }else{ ?>

                    <?php echo "<span>["; ?>

                    <?php echo ($config->blog->categoryName == 'full' or empty($article->category->abbr)) ? $article->category->name : $article->category->abbr; ?>

                    <?php echo "]</span>"; ?>

<?php } ?>

<?php } ?>            
          </div>
        </div>
      <?php endforeach; ?>

      <div class='clearfix pager'><?php echo $pager->show('right', 'short');?>


</div>
    </div>
    <div class='row blocks' data-region='blog_index-bottom'><?php echo $control->block->printRegion($layouts, 'blog_index', 'bottom', true);?>


</div>
  </div>
  <?php if(!empty($layouts['blog_index']['side']) and !(empty($sideFloat) || $sideFloat === 'hidden')){ ?>



  <div class='col-md-<?php echo $sideGrid; ?> col-side'>
    <side class='page-side'>
      <div class='blocks' data-region='blog_index-side'><?php echo $control->block->printRegion($layouts, 'blog_index', 'side');?>


</div>
    </side>
  </div>
  <?php } ?>

</div>
<div class='row'><?php echo $control->block->printRegion($layouts, 'blog_index', 'bottomBanner', true);?>


</div>
<?php $tpl = new RainTPL;$tpl->assign($this->var);$tpl->draw($control->loadModel('ui')->getEffectViewFile('default', 'blog', 'footer'));?>



