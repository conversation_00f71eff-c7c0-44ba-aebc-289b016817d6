<?php if(!class_exists('raintpl')){exit;}?><?php if(!defined("RUN_MODE")){ ?>



<?php echo die(); ?>

<?php } ?>

<?php if($extView = $control->getExtViewFile(TPL_ROOT . 'blog/header.html.php')){ ?>



<?php $tpl = new RainTPL;$tpl->assign($this->var);$tpl->draw($extView);?>

<?php return helper::cd(); ?>

<?php } ?>

<?php $navs=$this->var['navs']       = $control->loadModel('nav')->getNavs('desktop_blog');?>



<!DOCTYPE html>
<?php if(!empty($config->oauth->sina)){ ?>



<html lang='<?php echo $app->getClientLang(); ?>


' xmlns:wb="http://open.weibo.com/wb" class='m-<?php echo $thisModuleName; ?> m-<?php echo $thisModuleName; ?>-<?php echo $thisMethodName; ?>'>
<?php }else{ ?>

<html lang='<?php echo $app->getClientLang(); ?>


' class='m-<?php echo $thisModuleName; ?> m-<?php echo $thisModuleName; ?>-<?php echo $thisMethodName; ?>'>
<?php } ?>

<head>
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv='Content-Type' content='text/html; charset=utf-8' />
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta http-equiv="Cache-Control" content="no-transform" />
  <?php if(isset($mobileURL)){ ?>


 <link rel="alternate" media="only screen and (max-width: 640px)" href="<?php echo $sysURL . '/' . ltrim($mobileURL, '/'); ?>


"> <?php } ?>

  <?php if(isset($sourceURL)){ ?>


 <link rel="canonical" href="<?php echo $sysURL . ltrim($sourceURL, '/'); ?>


" > <?php } ?>

  <?php if(!isset($title)){ ?>


    <?php $title=$this->var['title']    = '';?>

<?php } ?>

  <?php if(!empty($title)){ ?>


    <?php $title=$this->var['title']   .= $lang->minus;?>

<?php } ?>

  <?php if(!isset($keywords)){ ?>


 <?php $keywords=$this->var['keywords'] = $config->site->keywords;?>

<?php } ?>

  <?php if(!isset($desc)){ ?>


     <?php $desc=$this->var['desc']     = $config->site->desc;?>

<?php } ?>


  <?php echo html::title($title . $config->site->name); ?>



  <?php echo html::meta('keywords',    strip_tags($keywords)); ?>



  <?php echo html::meta('description', strip_tags($desc)); ?>



  <?php if(isset($control->config->site->meta)){ ?>


 <?php echo $control->config->site->meta; ?>

<?php } ?>


  <?php echo css::import($webRoot . 'zui/css/min.css'); ?>



  <?php echo css::import($themeRoot . 'common/style.css'); ?>




  <?php $customCssFile=$this->var['customCssFile'] = $control->loadModel('ui')->getCustomCssFile(CHANZHI_TEMPLATE, CHANZHI_THEME);?>



  <?php if(file_exists($customCssFile)){ ?>


 <?php echo css::import($control->ui->getThemeCssUrl(CHANZHI_TEMPLATE, CHANZHI_THEME), '', $version = false); ?>

<?php } ?>


  <?php echo js::exportConfigVars(); ?>



  <?php echo js::set('theme', array('template' => CHANZHI_TEMPLATE, 'theme' => CHANZHI_THEME, 'device' => $control->app->clientDevice)); ?>



  <?php if($config->debug){ ?>



    <?php echo js::import($jsRoot . 'jquery/min.js'); ?>



    <?php echo js::import($jsRoot . 'zui/min.js'); ?>



    <?php echo js::import($jsRoot . 'chanzhi.js'); ?>



    <?php echo js::import($jsRoot . 'my.js'); ?>



  <?php }else{ ?>

    <?php if($control->config->cdn->open == 'open'){ ?>



      <?php echo js::import($control->config->cdn->host . $control->config->version . '/js/chanzhi.all.js', $version = false); ?>



    <?php }else{ ?>

      <?php echo js::import($jsRoot . 'chanzhi.all.js'); ?>

<?php } ?>

  <?php } ?>


  <?php if(isset($pageCSS)){ ?>


 <?php echo css::internal($pageCSS); ?>

<?php } ?>


  <?php echo isset($control->config->site->favicon) ? html::icon(json_decode($control->config->site->favicon)->webPath) : html::icon($webRoot . 'favicon.ico'); ?>



  <?php echo html::rss($control->createLink('rss', 'index', '', '', 'xml'), $config->site->name); ?>



  <?php echo js::set('lang', $lang->js); ?>



  <?php if(!empty($config->oauth->sina)){ ?>


                        <?php $sina=$this->var['sina'] = json_decode($config->oauth->sina);?>

<?php } ?>

  <?php if(!empty($config->oauth->qq)){ ?>


                          <?php $qq=$this->var['qq'] = json_decode($config->oauth->qq);?>

<?php } ?>

  <?php if(!empty($sina->verification)){ ?>


                         <?php echo $sina->verification;?>

<?php } ?>

  <?php if(!empty($qq->verification)){ ?>


                           <?php echo $qq->verification;?>

<?php } ?>

  <?php if(empty($sina->verification) && !empty($sina->widget)){ ?>


 <?php echo js::import('https://tjs.sjs.sinajs.cn/open/api/js/wb.js'); ?>

<?php } ?>


  <!--[if lt IE 9]>
    <?php if($config->debug){ ?>



      <?php echo js::import($jsRoot . 'html5shiv/min.js'); ?>



      <?php echo js::import($jsRoot . 'respond/min.js'); ?>



    <?php }else{ ?>

      <?php echo js::import($jsRoot . 'chanzhi.all.ie8.js'); ?>

<?php } ?>

  <![endif]-->
  <!--[if lt IE 10]>
      <?php if($config->debug){ ?>


  <?php echo js::import($jsRoot . 'jquery/placeholder/min.js'); ?>

<?php } ?>

      <?php if(!$config->debug){ ?>


 <?php echo js::import($jsRoot . 'chanzhi.all.ie9.js'); ?>

<?php } ?>

  <![endif]-->

  <?php $baseCustom=$this->var['baseCustom'] = isset($control->config->template->custom) ? json_decode($control->config->template->custom, true) : array();?>



  <?php if(!empty($baseCustom["CHANZHI_TEMPLATE"]["CHANZHI_THEME"]['js'])){ ?>


 <?php echo js::execute($baseCustom["CHANZHI_TEMPLATE"]["CHANZHI_THEME"]['js']); ?>

<?php } ?>

</head>
<body>
<div class='m-blog page-container page-blog'>
  <header id='header' class='clearfix'>
    <div id='headNav'><div class='wrapper'><?php echo commonModel::printTopBar() . commonModel::printLanguageBar(); ?>


</div></div>
    <div id='headTitle'>
      <div class="wrapper">
        <?php $logoSetting=$this->var['logoSetting'] = isset($control->config->site->logo) ? json_decode($control->config->site->logo) : new stdclass();?>



        <?php $logo=$this->var['logo'] = false;?>

        <?php if(isset($logoSetting->{CHANZHI_TEMPLATE}->themes->all)){ ?>


    <?php $logo=$this->var['logo'] = $logoSetting->{CHANZHI_TEMPLATE}->themes->all;?>

<?php } ?>

        <?php if(isset($logoSetting->{CHANZHI_TEMPLATE}->themes->{CHANZHI_THEME})){ ?>


  <?php $logo=$this->var['logo'] = $logoSetting->{CHANZHI_TEMPLATE}->themes->{CHANZHI_THEME};?>

<?php } ?>

        <?php if($logo){ ?>



          <?php $logo->extension = $control->loadModel('file')->getExtension($logo->pathname);$this->var['logo'] = $logo;?>



          <div id='siteLogo' data-ve='logo'>
            <?php echo html::a(helper::createLink('index'), html::image($control->loadModel('file')->printFileURL($logo), "class='logo' alt='{$control->config->company->name}' title='{$control->config->company->name}'")); ?>



          </div>
        <?php }else{ ?>

          <div id='siteName' data-ve='logo'><h2><?php echo html::a(helper::createLink('index'), $control->config->site->name); ?>


</h2></div>
        <?php } ?>

      </div>
    </div>
    <?php if(commonModel::isAvailable('search')){ ?>



    <div id='searchbar'>
      <form action='<?php echo helper::createLink('search'); ?>


' method='get' role='search'>
        <div class='input-group'>
          <?php $keywords=$this->var['keywords'] = ($control->app->getModuleName() == 'search') ? $control->session->serachIngWord : '';?>

          <?php echo html::input('words', $keywords, "class='form-control' placeholder=''"); ?>



          <?php if($control->config->requestType == 'GET'){ ?>


  <?php echo html::hidden($control->config->moduleVar, 'search') . html::hidden($control->config->methodVar, 'index'); ?>

<?php } ?>

          <div class='input-group-btn'>
            <button class='btn btn-default' type='submit'><i class='icon icon-search'></i></button>
          </div>
        </div>
      </form>
    </div>
    <?php } ?>

  </header>
  <nav id="blogNav" class="navbar navbar-default" data-ve='navbar' data-type='desktop_blog'>
    <div class='wrapper'>
      <ul class='nav navbar-nav'>
        <?php foreach($navs as $nav1): ?>



          <?php if(empty($nav1->children)){ ?>



            <li class='<?php echo $nav1->class; ?>'><?php echo html::a($nav1->url, $nav1->title, "target='$nav1->target'"); ?>


</li>
            <?php }else{ ?>

            <li class="<?php echo $nav1->hover . " " . $nav1->class; ?>">
                <?php echo html::a($nav1->url, $nav1->title . " <b class='caret'></b>", 'class="dropdown-toggle" data-toggle="dropdown"'); ?>



                <ul class='dropdown-menu' role='menu'>
                  <?php foreach($nav1->children as $nav2): ?>



                    <?php if(empty($nav2->children)){ ?>



                      <li class='<?php echo $nav2->class; ?>'><?php echo html::a($nav2->url, $nav2->title, "target='$nav2->target'"); ?>


</li>
                    <?php }else{ ?>

                      <li class='dropdown-submenu <?php echo $nav2->class; ?>'>
                        <?php echo html::a($nav2->url, $nav2->title, ($nav2->target != 'modal') ? "target='$nav2->target'" : ''); ?>



                        <ul class='dropdown-menu' role='menu'>
                          <?php foreach($nav2->children as $nav3): ?>



                          <li><?php echo html::a($nav3->url, $nav3->title, ($nav3->target != 'modal') ? "target='$nav3->target'" : ''); ?>


</li>
                          <?php endforeach; ?>

                        </ul>
                      </li>
                    <?php } ?>

                  <?php endforeach; ?><!-- end nav2 -->
                </ul>
            </li>
          <?php } ?>

        <?php endforeach; ?><!-- end nav1 -->
      </ul>
      <?php if(!isset($control->config->site->type) or $control->config->site->type != 'blog'){ ?>



        <ul class="nav navbar-nav navbar-right">
          <li><?php echo html::a(helper::createLink('index'), '<i class="icon-home icon-large"></i> ' . $lang->siteHome); ?>


</li>
        </ul>
      <?php } ?>

    </div>
  </nav>

  <div class='page-wrapper'>
    <div class='page-content'>
