<?php if(!defined('RUN_MODE')) die();?>.user-control-nav{margin-bottom: 20px;}
.break-all {word-wrap: break-word; word-break: break-all;}
.pager {margin:0px;}
@media (max-width: 480px) { .hidden-xxs {display: none} .page {font-size: 12px}}
@media (max-width: 400px) { .hidden-xxxs {display: none}}

#reset {max-width: 450px; margin: 20px auto;}
@media (max-width: 767px) {#reset {margin: 10px auto;} #reset .panel {margin: 20px auto; width: 100%;} #reset .panel-heading {padding: 0 0 10px 0;} #reset .panel-body {padding: 10px 0; min-height: inherit;} .panel-body > form {padding-right: 0;}}

#bindForm{height: 280;}
.nav-primary.nav-stacked > li.nav-heading{background-image:none;}
.container{margin: 60px auto 0; width: 800px;}
.container p{line-height:1.5;}

.alert.with-icon > .icon, .alert.with-icon > .icon + .content {padding: 10px 20px 20px;}
.alert.with-icon > .icon {padding-left: 35px;}
.alert-deny {max-width: 500px; margin: 8% auto; padding: 0; background-color: #FFF; border: 1px solid #DDD; box-shadow: 0px 2px 20px rgba(0, 0, 0, 0.2); border-radius: 6px;}
.btn-link {border-color: none!important}
