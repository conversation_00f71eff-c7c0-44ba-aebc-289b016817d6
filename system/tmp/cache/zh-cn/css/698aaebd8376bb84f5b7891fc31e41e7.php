<?php if(!defined('RUN_MODE')) die();?>.sitemap-tree{margin: 30px 0 20px 0;position: relative;border-top: 1px dashed #e5e5e5;padding: 25px 0 20px 0;}
@media (min-width: 992px) {.sitemap-tree { padding-left: 80px; }}
.sitemap-tree > h4{display: block;position: absolute;left: 0;top: -25px;padding: 5px 15px;border-radius: 15px;background-color: #f1f1f1;font-size: 14px;line-height: 20px;font-weight: bold;color: #666;}
.sitemap-tree .tree > li{float: left;min-width: 150px;}

#header {padding: 0; margin-bottom: 14px;}
#headNav {min-height: 30px; line-height: 30px; padding: 0; margin-bottom: 8px;}
#headNav, #headTitle {position: static; display: block;}
#headNav > .row {margin: 0}
#headTitle > .row, #headNav > .row {display: table; width: 100%; margin: 0}
#headNav > .row > #siteNav,
#headNav > .row > #siteSlogan,
#headNav > .row > #searchbar,
#headTitle > .row > #siteTitle,
#headTitle > .row > #searchbar {display: table-cell; vertical-align: middle;}

#headTitle {padding: 0;}
#siteNav {text-align: right; float: right; display: inline-block !important;}
@media (max-width: 767px){#siteNav {padding-left: 8px; padding-right: 8px;} }

#searchbar {max-width: initial;}
#searchbar > form {max-width: 200px; float: right;}
#navbar .navbar-nav {width: 100%}
#navbarCollapse {padding: 0;}
#navbar .navbar-nav {margin: 0;}
#navbar li.nav-item-searchbar {float: right;}
#navbar li.nav-item-searchbar #searchbar > form {margin: 4px;}














#panel-404 {padding: 40px 80px 60px;}
#panel-404 h1 {margin-bottom: 40px;}
#panel-404 form {max-width: 300px}
.screen-phone #panel-404 {padding: 20px 20px 30px;}
