<?php
/**
 * Session测试脚本
 * Session test script
 */

// 启动session
session_start();

echo "<h1>Session 测试</h1>";

// 显示session配置
echo "<h2>Session 配置</h2>";
echo "<p>Session Save Path: " . session_save_path() . "</p>";
echo "<p>Session Name: " . session_name() . "</p>";
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>Session Status: " . session_status() . "</p>";

// 检查session目录是否可写
$sessionPath = session_save_path();
echo "<p>Session Path Writable: " . (is_writable($sessionPath) ? 'Yes' : 'No') . "</p>";
echo "<p>Session Path Exists: " . (is_dir($sessionPath) ? 'Yes' : 'No') . "</p>";

// 测试session写入
if (!isset($_SESSION['test_counter'])) {
    $_SESSION['test_counter'] = 0;
}
$_SESSION['test_counter']++;

echo "<h2>Session 数据测试</h2>";
echo "<p>访问次数: " . $_SESSION['test_counter'] . "</p>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";

// 显示所有session数据
echo "<h2>所有 Session 数据</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// 显示session文件
if (is_dir($sessionPath)) {
    echo "<h2>Session 文件列表</h2>";
    $files = scandir($sessionPath);
    echo "<ul>";
    foreach ($files as $file) {
        if ($file != '.' && $file != '..') {
            $filePath = $sessionPath . '/' . $file;
            $fileSize = filesize($filePath);
            $fileTime = date('Y-m-d H:i:s', filemtime($filePath));
            echo "<li>$file (Size: $fileSize bytes, Modified: $fileTime)</li>";
        }
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>Session目录不存在: $sessionPath</p>";
}

// 显示PHP信息
echo "<h2>PHP Session 配置</h2>";
echo "<table border='1'>";
echo "<tr><th>配置项</th><th>值</th></tr>";
$sessionConfigs = [
    'session.save_handler',
    'session.save_path',
    'session.name',
    'session.auto_start',
    'session.cookie_lifetime',
    'session.cookie_path',
    'session.cookie_domain',
    'session.cookie_httponly',
    'session.use_cookies',
    'session.use_only_cookies',
    'session.gc_maxlifetime',
    'session.gc_probability',
    'session.gc_divisor'
];

foreach ($sessionConfigs as $config) {
    echo "<tr><td>$config</td><td>" . ini_get($config) . "</td></tr>";
}
echo "</table>";

echo "<p><a href='test_session.php'>刷新页面</a></p>";
?>
